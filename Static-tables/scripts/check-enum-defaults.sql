-- Script to find all columns with enum default values that might need updating

-- Find all columns using enum types with their default values
SELECT 
    t.table_schema,
    t.table_name,
    c.column_name,
    c.data_type,
    c.udt_name AS enum_type,
    c.column_default,
    CASE 
        WHEN c.column_default IS NOT NULL THEN 'HAS DEFAULT'
        ELSE 'NO DEFAULT'
    END AS default_status
FROM information_schema.tables t
JOIN information_schema.columns c ON c.table_name = t.table_name AND c.table_schema = t.table_schema
WHERE c.udt_name LIKE 'enum_%'
  AND t.table_schema = 'public'  -- Adjust schema if needed
ORDER BY t.table_name, c.column_name;

-- Check specific enum types and their current values
SELECT 
    t.typname AS enum_name,
    e.enumlabel AS enum_value,
    e.enumsortorder AS sort_order
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname IN (
    'enum_services_type_of_service',
    'enum_services_type_of_cover', 
    'enum_services_status',
    'enum_services_calculation_method',
    'enum_services_billing_method',
    'enum_partners_calculation_method'
)
ORDER BY t.typname, e.enumsortorder;

-- Find columns that might have problematic default values
SELECT 
    schemaname,
    tablename,
    attname AS column_name,
    adsrc AS default_value
FROM pg_attrdef d
JOIN pg_attribute a ON d.adrelid = a.attrelid AND d.adnum = a.attnum
JOIN pg_class c ON a.attrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public'
  AND adsrc LIKE '%enum_%'
ORDER BY tablename, attname;
