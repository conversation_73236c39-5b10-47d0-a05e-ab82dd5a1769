#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to identify and generate SQL to fix enum default values
 * Run this before executing the enum migration to identify potential issues
 */

const { Pool } = require('pg');
const appConfig = require('../config/app-config');

class EnumDefaultFixer {
  constructor() {
    this.pool = new Pool({
      host: appConfig.db.host,
      port: appConfig.db.port,
      database: appConfig.db.name,
      user: appConfig.db.user,
      password: appConfig.db.password,
    });
  }

  async checkEnumDefaults() {
    console.log('🔍 Checking enum columns with default values...\n');

    try {
      // Find all enum columns with default values
      const query = `
        SELECT 
          t.table_name,
          c.column_name,
          c.udt_name AS enum_type,
          c.column_default,
          CASE 
            WHEN c.column_default IS NOT NULL THEN true
            ELSE false
          END AS has_default
        FROM information_schema.tables t
        JOIN information_schema.columns c ON c.table_name = t.table_name AND c.table_schema = t.table_schema
        WHERE c.udt_name LIKE 'enum_%'
          AND t.table_schema = 'public'
          AND c.column_default IS NOT NULL
        ORDER BY t.table_name, c.column_name;
      `;

      const result = await this.pool.query(query);
      
      if (result.rows.length === 0) {
        console.log('✅ No enum columns with default values found.');
        return;
      }

      console.log('📋 Found enum columns with default values:');
      console.log('=' .repeat(80));
      
      const fixes = [];
      
      for (const row of result.rows) {
        console.log(`Table: ${row.table_name}`);
        console.log(`Column: ${row.column_name}`);
        console.log(`Enum Type: ${row.enum_type}`);
        console.log(`Current Default: ${row.column_default}`);
        
        // Extract the default value (remove quotes and type casting)
        const defaultValue = this.extractDefaultValue(row.column_default);
        const uppercaseDefault = defaultValue.toUpperCase();
        
        console.log(`Suggested New Default: '${uppercaseDefault}'`);
        
        // Generate fix SQL
        const fixSql = this.generateFixSQL(row.table_name, row.column_name, row.enum_type, uppercaseDefault);
        fixes.push(fixSql);
        
        console.log('-'.repeat(80));
      }

      console.log('\n🔧 Generated SQL fixes:');
      console.log('=' .repeat(80));
      
      fixes.forEach((fix, index) => {
        console.log(`-- Fix ${index + 1}:`);
        console.log(fix);
        console.log('');
      });

      console.log('💡 Instructions:');
      console.log('1. Review the suggested fixes above');
      console.log('2. Update your migration file to include the correct default values');
      console.log('3. The migration already includes steps to drop and restore defaults');
      console.log('4. Uncomment and modify the default value lines in your migration');

    } catch (error) {
      console.error('❌ Error checking enum defaults:', error);
    } finally {
      await this.pool.end();
    }
  }

  extractDefaultValue(columnDefault) {
    // Remove quotes, type casting, and extract the actual value
    // Examples: 
    // "'initial'::enum_services_status" -> "initial"
    // "initial" -> "initial"
    
    let value = columnDefault;
    
    // Remove type casting (::enum_type)
    value = value.replace(/::[a-zA-Z_]+/g, '');
    
    // Remove quotes
    value = value.replace(/^'|'$/g, '');
    
    return value;
  }

  generateFixSQL(tableName, columnName, enumType, newDefaultValue) {
    return `-- Update default for ${tableName}.${columnName}
ALTER TABLE ${tableName} ALTER COLUMN ${columnName} SET DEFAULT '${newDefaultValue}'::${enumType};`;
  }

  async checkEnumValues() {
    console.log('\n🔍 Current enum values:');
    console.log('=' .repeat(50));

    const enumQuery = `
      SELECT 
        t.typname AS enum_name,
        e.enumlabel AS enum_value,
        e.enumsortorder AS sort_order
      FROM pg_type t 
      JOIN pg_enum e ON t.oid = e.enumtypid  
      WHERE t.typname LIKE 'enum_%'
      ORDER BY t.typname, e.enumsortorder;
    `;

    try {
      const result = await this.pool.query(enumQuery);
      
      let currentEnum = '';
      for (const row of result.rows) {
        if (row.enum_name !== currentEnum) {
          if (currentEnum !== '') console.log('');
          console.log(`📝 ${row.enum_name}:`);
          currentEnum = row.enum_name;
        }
        console.log(`   ${row.sort_order}. ${row.enum_value}`);
      }
    } catch (error) {
      console.error('❌ Error checking enum values:', error);
    }
  }
}

// Run the checker
async function main() {
  const fixer = new EnumDefaultFixer();
  
  try {
    await fixer.checkEnumDefaults();
    await fixer.checkEnumValues();
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

main();
