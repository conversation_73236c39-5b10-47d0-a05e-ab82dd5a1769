-- Script to find all tables and columns using enum_services_type_of_service

-- Find all columns using the enum type
SELECT 
    t.table_schema,
    t.table_name,
    c.column_name,
    c.data_type,
    c.udt_name
FROM information_schema.tables t
JOIN information_schema.columns c ON c.table_name = t.table_name AND c.table_schema = t.table_schema
WHERE c.udt_name = 'enum_services_type_of_service'
ORDER BY t.table_schema, t.table_name, c.column_name;

-- Check current enum values
SELECT 
    t.typname AS enum_name,
    e.enumlabel AS enum_value,
    e.enumsortorder AS sort_order
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname = 'enum_services_type_of_service'
ORDER BY e.enumsortorder;

-- Count records for each enum value (replace 'your_table.your_column' with actual table and column)
-- SELECT 
--     your_column,
--     COUNT(*) as count
-- FROM your_table 
-- WHERE your_column IS NOT NULL
-- GROUP BY your_column
-- ORDER BY count DESC;
