const BaseJoi = require("joi");
const Extension = require("joi-date-extensions");
const Joi = BaseJoi.extend(Extension);

module.exports = {
    create: {
        body: Joi.object().keys({
            activities: Joi.array().items(
                Joi.object().keys({
                    code: Joi.string().min(2).max(255).required().label("activity_code"),
                    
                })
            ).required(),
            business_name: Joi.string().min(2).max(255).allow("", null).label("business_name"),
        }),
    }
};
