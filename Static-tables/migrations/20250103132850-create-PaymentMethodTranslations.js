/** @type {import('sequelize-cli').Migration} */

module.exports = {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.createTable("payment_method_translations", {
            id: {
                type: Sequelize.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            payment_method_code: {
                type: Sequelize.STRING,
                allowNull: false,
                references: {
                    model: "payment_methods",
                    key: "code"
                },
                onUpdate: "CASCADE",
                onDelete: "CASCADE"
            },
            label: {
                type: Sequelize.STRING,
                allowNull: false
            },
            active: {
                type: Sequelize.BOOLEAN,
                defaultValue: true
            },
            requires_bank_account: {
                type: Sequelize.BOOLEAN,
                defaultValue: false
            },
            manual_transaction: {
                type: Sequelize.BOOLEAN,
                defaultValue: false
            },
            exchange_file: {
                type: Sequelize.BOOLEAN,
                defaultValue: false
            },
            bank_card: {
                type: Sequelize.BOOLEAN,
                defaultValue: false
            },
            language_code: {
                type: Sequelize.STRING,
                allowNull: false
            },
            createdAt: {
                allowNull: false,
                type: Sequelize.DATE,
                defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
            },
            updatedAt: {
                allowNull: false,
                type: Sequelize.DATE,
                defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
            },
            deletedAt: {
                allowNull: true,
                type: Sequelize.DATE,
            },
        });

        await queryInterface.addConstraint("payment_method_translations", {
            fields: ["payment_method_code", "language_code"],
            type: "unique",
            name: "unique_payment_method_language"
        });
    },

    down: async (queryInterface) => {
        await queryInterface.dropTable("payment_method_translations");
    }
};