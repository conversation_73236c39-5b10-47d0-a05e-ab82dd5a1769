<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.25.xsd">

    <changeSet id="update-enum-services-type-uppercase-001" author="developer">
        <comment>Update enum_services_type_of_service to use uppercase values</comment>
        
        <!-- Step 1: Create new enum with uppercase values -->
        <sql>
            CREATE TYPE enum_services_type_of_service_new AS ENUM (
                'MAINTENANCE', 
                'INSURANCE', 
                'REPLACEMENT_VEHICLE', 
                'PECUNIARY_LOSS', 
                'FINANCIAL_LOSS', 
                'ADMINISTRATIVE_FEES'
            );
        </sql>
        
        <!-- Step 2: Update all tables using the enum (replace 'your_table_name' and 'your_column_name' with actual names) -->
        <sql>
            -- Update services table (example - adjust table and column names as needed)
            ALTER TABLE services 
            ALTER COLUMN type_of_service TYPE enum_services_type_of_service_new 
            USING UPPER(type_of_service::text)::enum_services_type_of_service_new;
        </sql>
        
        <!-- Step 3: Drop old enum type -->
        <sql>
            DROP TYPE enum_services_type_of_service;
        </sql>
        
        <!-- Step 4: Rename new enum to original name -->
        <sql>
            ALTER TYPE enum_services_type_of_service_new RENAME TO enum_services_type_of_service;
        </sql>
        
        <rollback>
            <!-- Rollback: Recreate original enum with lowercase values -->
            <sql>
                CREATE TYPE enum_services_type_of_service_old AS ENUM (
                    'Maintenance', 
                    'Insurance', 
                    'Replacement_vehicle', 
                    'Pecuniary_loss', 
                    'Financial_loss', 
                    'Administrative_fees'
                );
                
                ALTER TABLE services 
                ALTER COLUMN type_of_service TYPE enum_services_type_of_service_old 
                USING LOWER(REPLACE(type_of_service::text, '_', '_'))::enum_services_type_of_service_old;
                
                DROP TYPE enum_services_type_of_service;
                ALTER TYPE enum_services_type_of_service_old RENAME TO enum_services_type_of_service;
            </sql>
        </rollback>
    </changeSet>

</databaseChangeLog>
