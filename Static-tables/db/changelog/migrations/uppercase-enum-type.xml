<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="HoussemMOUSSA (generated)" id="enum-1">
        <sql>
            CREATE TYPE enum_services_type_of_service_new AS ENUM (
                'MAINTENANCE',
                'INSURANCE',
                'REPLACEMENT_VEHICLE',
                'PECUNIARY_LOSS',
                'FINANCIAL_LOSS',
                'ADMINISTRATIVE_FEES'
            );

            -- Step 2: Update all tables using the old enum
            ALTER TABLE services
            ALTER COLUMN type_of_service TYPE enum_services_type_of_service_new
                USING UPPER(type_of_service::text)::enum_services_type_of_service_new;

            -- Step 3: Drop the old enum type
            DROP TYPE enum_services_type_of_service;

            -- Step 4: Rename the new enum to the original name
            ALTER TYPE enum_services_type_of_service_new RENAME TO enum_services_type_of_service;
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="enum-2">
        <sql>
            -- Create new enum with uppercase values
            CREATE TYPE enum_services_type_of_cover_new AS ENUM (
                'THIRD_PARTY',
                'MATERIAL'
            );

            -- Update all tables using the old enum
            ALTER TABLE services
            ALTER COLUMN type_of_cover TYPE enum_services_type_of_cover_new
                USING UPPER(type_of_cover::text)::enum_services_type_of_cover_new;

            -- Drop the old enum type
            DROP TYPE enum_services_type_of_cover;

            -- Rename the new enum to the original name
            ALTER TYPE enum_services_type_of_cover_new RENAME TO enum_services_type_of_cover;
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="enum-3">
        <modifySql>
            <
        </modifySql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="enum-3">
        <sql>
            -- Create new enum with uppercase values
            CREATE TYPE enum_services_status_new AS ENUM (
                'INITIAL',
                'ACTIVE',
                'EXPIRED'
            );

            -- Update all tables using the old enum
            ALTER TABLE services
            ALTER COLUMN status TYPE enum_services_status_new
                USING UPPER(status::text)::enum_services_status_new;

            -- Drop the old enum type
            DROP TYPE enum_services_status;

            -- Rename the new enum to the original name
            ALTER TYPE enum_services_status_new RENAME TO enum_services_status;
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="enum-4">
        <sql>
            -- Create new enum with uppercase values
            CREATE TYPE enum_services_calculation_method_new AS ENUM (
                'PERCENTAGE',
                'FIXED_AMOUNT',
                'MATRIX'
            );

            -- Update all tables using the old enum
            ALTER TABLE services
            ALTER COLUMN calculation_method TYPE enum_services_calculation_method_new
                USING UPPER(calculation_method::text)::enum_services_calculation_method_new;

            -- Drop the old enum type
            DROP TYPE enum_services_calculation_method;

            -- Rename the new enum to the original name
            ALTER TYPE enum_services_calculation_method_new RENAME TO enum_services_calculation_method;
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="enum-5">
        <sql>
            -- Create new enum with uppercase values
            CREATE TYPE enum_services_billing_method_new AS ENUM (
                'SUBSCRIPTION',
                'FLAT'
            );

            -- Update all tables using the old enum
            ALTER TABLE services
            ALTER COLUMN billing_method TYPE enum_services_billing_method_new
                USING UPPER(billing_method::text)::enum_services_billing_method_new;

            -- Drop the old enum type
            DROP TYPE enum_services_billing_method;

            -- Rename the new enum to the original name
            ALTER TYPE enum_services_billing_method_new RENAME TO enum_services_billing_method;
        </sql>
    </changeSet>
    <changeSet author="HoussemMOUSSA (generated)" id="enum-6">
        <sql>
            -- Create new enum with uppercase values
            CREATE TYPE enum_partners_calculation_method_new AS ENUM (
                'PERCENTAGE',
                'FIXED_AMOUNT'
            );

            -- Update all tables using the old enum
            ALTER TABLE partners
            ALTER COLUMN calculation_method TYPE enum_partners_calculation_method_new
                USING UPPER(calculation_method::text)::enum_partners_calculation_method_new;

            -- Drop the old enum type
            DROP TYPE enum_partners_calculation_method;

            -- Rename the new enum to the original name
            ALTER TYPE enum_partners_calculation_method_new RENAME TO enum_partners_calculation_method;
        </sql>
    </changeSet>
</databaseChangeLog>
