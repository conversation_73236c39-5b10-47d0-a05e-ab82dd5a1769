# Common configuration for all profiles
spring:
  application:
    name: authmodule
  datasource:
    driver-class-name: org.postgresql.Driver
    hikari:
      maxLifetime: 270000
  jpa:
    open-in-view: 'false'
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: 'true'
    hibernate:
      naming:
        physical-strategy: com.datatricks.authmodule.config.CustomHibernateNamingStrategy
      ddl-auto: validate
  liquibase:
    change-log: classpath:db/changeLog/db.changelog-master.xml
    enabled: true
server:
  port: 8810
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
application:
    security:
      jwt:
        secret-key: MIHcAgEBBEIB9D1/yfXxYbvgarUXve1XWs0RUJQW15N3//wr8HSAtNe0sCP/PEQOG1udSMPccdNL1jjB7ep3Xw2GwTum0xqJIAGgBwYFK4EEACOhgYkDgYYABAAQzFHMqFC30I7wTh53nugc4vusR9UXJGbpApE5l+C0aRSJ671vXUD1eaiBylRoUOcBV9I0+le/fq5RjyT6fTWYjQHkk9dU06wmo7yXDqngCWizRULxAKLZFw20MJg7ODfNW1GzTaZMd/T7ehJ6RIFXhsDJ058kJ7oimDutCwsHboAgTw==
        expiration: 86400000 # 1 day
        refresh-token:
          expiration: 604800000 # 7 days

# local profile configuration
---
spring:
  config:
    activate:
      on-profile: local
  datasource:
    url: ****************************************
    username: postgres
    password: U6GjpKQpsrwjZI
logging:
  level:
    root: info
    com.netflix.zuul: info
    org.springframework.cloud.netflix: info
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
# docker local profile configuration
---
spring:
  config:
    activate:
      on-profile: docker-local
  datasource:
    url: ***************************************
    username: postgres
    password: U6GjpKQpsrwjZI
logging:
  level:
    root: info
    com.netflix.zuul: info
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
# Dev profile configuration
---
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?prepareThreshold=0
    username: ${DB_USER}
    password: ${DB_PASSWORD}

logging:
  level:
    root: info
    com.netflix.zuul: info
    org.springframework.cloud.netflix: info
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
# Test profile configuration
---
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?prepareThreshold=0
    username: ${DB_USER}
    password: ${DB_PASSWORD}

logging:
  level:
    root: info
    com.netflix.zuul: info
    org.springframework.cloud.netflix: info
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
# Staging profile configuration
---
spring:
  config:
    activate:
      on-profile: staging
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?prepareThreshold=0
    username: ${DB_USER}
    password: ${DB_PASSWORD}

logging:
  level:
    root: info
    com.netflix.zuul: info
    org.springframework.cloud.netflix: info