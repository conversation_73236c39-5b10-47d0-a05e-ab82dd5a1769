<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:pro="http://www.liquibase.org/xml/ns/pro"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd
      http://www.liquibase.org/xml/ns/pro
      http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd">
    <changeSet author="AhmedKHIARI (generated)" id="0000013-04">
        <sql>
            CREATE OR REPLACE VIEW DT_TIMETABLES_SUMMARY_VIEW AS
WITH principal_bank_accounts AS (
    SELECT ACTOR_ID, IBAN
    FROM DT_BANK_ACCOUNTS
    WHERE DELETED_AT IS NULL AND IS_PRINCIPAL = TRUE
),
     filtered_business_payments AS (
         SELECT *
         FROM DT_BUSINESS_SETTLEMENT_MEANS
         WHERE DELETED_AT IS NULL
     ),
     filtered_bank_accounts AS (
         SELECT *
         FROM DT_BANK_ACCOUNTS
         WHERE DELETED_AT IS NULL
     ),
     filtered_roles AS (
         SELECT *
         FROM DT_ROLES
     )
SELECT
    company.REFERENCE AS company_reference,
    company.NAME AS company_name,
    actor.REFERENCE AS actor_reference,
    actor.NAME AS actor_name,
    actor.ID AS actor_id,
    c.REFERENCE AS contract_reference,
    c.id AS contract_id,
    company.ID AS company_id,
    c.ACTIVITY_CODE AS contract_activity_code,
    c.PRODUCT_CODE AS contract_product_code,
    T.STATUS AS timetable_status,
    Ti.STATUS AS timetable_item_status,
    L.ID AS level_id,
    Ti.ID,
    Ti.DEPRECIATION,
    Ti.DUE_DATE,
    Ti.END_DATE,
    Ti.INTEREST,
    Ti.NOMINAL_RATE,
    Ti.RATE,
    Ti.RENT,
    Ti.RESIDUAL_VALUE,
    Ti.START_DATE,
    Ti.TAX_AMOUNT,
    Ti.amortization,
    Ti.unpaid,
    Ti.TIMETABLE_ID,
    ROL.CODE AS role_code,
    COALESCE(B.IBAN, P.IBAN)                                                          AS iban,
    (
        SELECT ID
        FROM DT_ADDRESSES a
        WHERE a.ACTOR_ID = actor.ID
            FETCH FIRST 1 ROWS ONLY
    )                                                                                 AS client_address_id,
    COALESCE(R.SEPARATE_INVOICE, A.SEPARATE_INVOICE, RE.SEPARATE_INVOICE, FALSE) AS is_separate_billing,
    CASE
        WHEN R.ID IS NOT NULL THEN R.ALLOCATION_CODE
        WHEN A.ID IS NOT NULL THEN A.ALLOCATION_CODE
        WHEN RE.ID IS NOT NULL THEN RE.ALLOCATION_CODE
        ELSE NULL
        END                                                                           AS analytic_input,
    CASE
        WHEN R.ID IS NOT NULL THEN R.TAX
        WHEN A.ID IS NOT NULL THEN A.TAX
        WHEN RE.ID IS NOT NULL THEN RE.TAX
        ELSE NULL
        END                                                                           AS tax,
    CASE
        WHEN R.ID IS NOT NULL THEN R.TAX
        WHEN A.ID IS NOT NULL THEN A.TAX
        WHEN RE.ID IS NOT NULL THEN RE.TAX
        ELSE NULL
        END        AS tax_code,
    Ti.DELETED_AT,
    Ti.CREATED_AT,
    Ti.MODIFIED_AT
FROM
    DT_TIMETABLE_ITEMS Ti
        JOIN DT_TIMETABLES T ON T.ID = Ti.TIMETABLE_ID AND T.DELETED_AT IS NULL
        JOIN DT_LEVELS L ON L.TIMETABLE_ID = T.ID AND L.DELETED_AT IS NULL
        LEFT JOIN DT_RENTALS R ON R.TIMETABLE_ID = T.ID AND R.DELETED_AT IS NULL
        LEFT JOIN DT_ACCESSORIES A ON A.TIMETABLE_ID = T.ID AND A.DELETED_AT IS NULL
        LEFT JOIN DT_RETRIBUTIONS RE ON RE.TIMETABLE_ID = T.ID AND RE.DELETED_AT IS NULL
        JOIN DT_CONTRACT_ACTORS Ca ON Ca.ID = COALESCE(R.CONTRACT_ACTOR_ID, A.CONTRACT_ACTOR_ID, RE.CONTRACT_ACTOR_ID) AND Ca.DELETED_AT IS NULL
        JOIN DT_CONTRACTS C ON C.REFERENCE = Ca.CONTRACT_REFERENCE AND C.DELETED_AT IS NULL
        JOIN DT_ACTORS company ON company.REFERENCE = C.BUSINESS_REFERENCE AND company.DELETED_AT IS NULL
        JOIN DT_ACTORS actor ON actor.REFERENCE = Ca.ACTOR_REFERENCE AND actor.DELETED_AT IS NULL
        LEFT JOIN filtered_business_payments DBP ON company.ID = DBP.ACTOR_ID AND DBP.PAYMENT_TYPE = 'Encashment'
        LEFT JOIN filtered_bank_accounts B ON B.ID = DBP.BANK_ACCOUNT_ID
        LEFT JOIN principal_bank_accounts P ON P.ACTOR_ID = company.ID
        LEFT JOIN filtered_roles ROL ON ROL.CODE = Ca.ROLE_CODE
WHERE
    (Ti.STATUS IS NULL OR Ti.STATUS IN ('MARKED_FOR_FAILED', 'MARKED_FOR_COMPLETION', 'IN_PROGRESS'))
  AND Ti.RENT != 0 -- ensure that we don't get the extra items (debut + end)
  AND Ti.DELETED_AT IS NULL;
        </sql>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="0000013-05">
        <sql>
            CREATE OR REPLACE VIEW DT_TIMETABLES_SUMMARY_VIEW_SEPARATE_BILLING AS
            select * from DT_TIMETABLES_SUMMARY_VIEW where IS_SEPARATE_BILLING = true;
        </sql>
    </changeSet>
    <changeSet author="AhmedKHIARI (generated)" id="0000013-06">
        <sql>
            CREATE OR REPLACE VIEW DT_TIMETABLES_SUMMARY_VIEW_NOT_SEPARATE_BILLING AS
            select * from DT_TIMETABLES_SUMMARY_VIEW where IS_SEPARATE_BILLING = false;
        </sql>
    </changeSet>
</databaseChangeLog>
