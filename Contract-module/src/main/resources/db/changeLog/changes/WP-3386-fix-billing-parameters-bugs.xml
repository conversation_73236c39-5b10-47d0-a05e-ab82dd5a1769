<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">


<changeSet id="554144" author="author">
        <delete tableName="dt_payment_methods">
            <where>1=1</where>
        </delete>
        <insert tableName="dt_payment_methods">
            <column name="id" valueNumeric="1" />
            <column name="code" value="VIRAUTO" />
            <column name="label" value="Virement automatique" />
            <column name="requires_bank_account" valueBoolean="true" />
            <column name="language" value="FR" />
        </insert>
        <insert tableName="dt_payment_methods">
            <column name="id" valueNumeric="2" />
            <column name="code" value="PRLAUTO" />
            <column name="label" value="Prélèvement automatique" />
            <column name="requires_bank_account" valueBoolean="true" />
            <column name="language" value="FR" />
        </insert>
        <insert tableName="dt_payment_methods">
            <column name="id" valueNumeric="3" />
            <column name="code" value="VIRMANU" />
            <column name="label" value="Virement manuel" />
            <column name="requires_bank_account" valueBoolean="false" />
            <column name="language" value="FR" />
        </insert>
        <insert tableName="dt_payment_methods">
            <column name="id" valueNumeric="4" />
            <column name="code" value="CHQ" />
            <column name="label" value="Chèque" />
            <column name="requires_bank_account" valueBoolean="true" />
            <column name="language" value="FR" />
        </insert>
        <insert tableName="dt_payment_methods">
            <column name="id" valueNumeric="5" />
            <column name="code" value="PRLMANU" />
            <column name="label" value="Prélèvement manuel" />
            <column name="requires_bank_account" valueBoolean="false" />
            <column name="language" value="FR" />
        </insert>
        <insert tableName="dt_payment_methods">
            <column name="id" valueNumeric="6" />
            <column name="code" value="MA" />
            <column name="label" value="Mandat administratif" />
            <column name="requires_bank_account" valueBoolean="false" />
            <column name="language" value="FR" />
        </insert>
        <insert tableName="dt_payment_methods">
            <column name="id" valueNumeric="7" />
            <column name="code" value="CB" />
            <column name="label" value="Carte bancaire" />
            <column name="requires_bank_account" valueBoolean="false" />
            <column name="language" value="FR" />
        </insert>
        <insert tableName="dt_payment_methods">
            <column name="id" valueNumeric="8" />
            <column name="code" value="CAPI" />
            <column name="label" value="Capitalisation" />
            <column name="requires_bank_account" valueBoolean="false" />
            <column name="language" value="FR" />
        </insert>
        <insert tableName="dt_payment_methods">
            <column name="id" valueNumeric="9" />
            <column name="code" value="VIRTUAL" />
            <column name="label" value="Compte bancaire virtuel" />
            <column name="requires_bank_account" valueBoolean="false" />
            <column name="language" value="FR" />
        </insert>
        <insert tableName="dt_payment_methods">
            <column name="id" valueNumeric="10" />
            <column name="code" value="LCR" />
            <column name="label" value="Traite - LRC" />
            <column name="requires_bank_account" valueBoolean="false" />
            <column name="language" value="FR" />
        </insert>
        <insert tableName="dt_payment_methods">
            <column name="id" valueNumeric="11" />
            <column name="code" value="RETGAR" />
            <column name="label" value="Rétention de garantie" />
            <column name="requires_bank_account" valueBoolean="false" />
            <column name="language" value="FR" />
        </insert>
    </changeSet>

</databaseChangeLog>