package com.datatricks.contracts.controller;

import com.datatricks.contracts.exception.handler.InformativeMessage;
import com.datatricks.contracts.model.dto.ContractActorAddressDto;
import com.datatricks.contracts.model.dto.ContractActorAddressInput;
import com.datatricks.contracts.model.dto.SingleResultDto;
import com.datatricks.contracts.service.ContractActorAddressService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.datatricks.contracts.utils.ContractUtils.handleException;

@RestController
@RequestMapping("/api")
public class ContractActorAddressController {
    private final ContractActorAddressService contractActorAddressService;

    @Autowired
    ContractActorAddressController(
            ContractActorAddressService contractActorAddressService) {
        this.contractActorAddressService = contractActorAddressService;
    }

    @Cacheable(value = "contractActorAddresses", key = "#contract_actor_id")
    @GetMapping(value = "/v1/contract-actors/{contract_actor_id}/contract-actor-addresses")
    public ResponseEntity<SingleResultDto<ContractActorAddressDto>> getContractActorsAddresses(@PathVariable Long contract_actor_id) {
        try {
            return contractActorAddressService.getContractActorAddresses(contract_actor_id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            evict = {
                    @CacheEvict(value = "contractActorAddresses", key = "#contract_actor_id"),
                    @CacheEvict(value = "contractActorAddressesById", key = "#contract_actor_id + ':' + #contract_actor_address_id")
            }
    )
    @PostMapping(value = "/v1/contract-actors/{contract_actor_id}/contract-actor-addresses")
    public ResponseEntity<SingleResultDto<ContractActorAddressDto>> addContractActorAddress(
            @PathVariable Long contract_actor_id,
            @RequestBody @Valid ContractActorAddressInput contractActorAddressInput) {
        return contractActorAddressService.createContractActorAddress(contract_actor_id, contractActorAddressInput);
    }

    @PutMapping(value = "/v1/contract-actors/{contract_actor_id}/contract-actor-addresses/{contract_actor_address_id}")
    public ResponseEntity<SingleResultDto<ContractActorAddressDto>> updateContractActorAddress(
            @PathVariable Long contract_actor_id,
            @PathVariable Long contract_actor_address_id,
            @RequestBody @Valid ContractActorAddressInput contractActorAddressInput) {
        try {
            return contractActorAddressService.updateContractActorAddress(
                    contract_actor_id, contract_actor_address_id, contractActorAddressInput);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Caching(
            evict = {
                    @CacheEvict(value = "contractActorAddresses", key = "#contract_actor_id"),
                    @CacheEvict(value = "contractActorAddressesById", key = "#contract_actor_id + ':' + #contract_actor_address_id")
            }
    )
    @DeleteMapping(value = "/v1/contract-actors/{contract_actor_id}/contract-actor-addresses/{contract_actor_address_id}")
    public ResponseEntity<InformativeMessage> deleteContractActorAddress(
            @PathVariable Long contract_actor_id,
            @PathVariable Long contract_actor_address_id) {
        try {
        return contractActorAddressService.deleteContractActorAddress(contract_actor_id, contract_actor_address_id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }

    @Cacheable(value = "contractActorAddressesById", key = "#contract_actor_id + ':' + #contract_actor_address_id")
    @GetMapping(value = "/v1/contract-actors/{contract_actor_id}/contract-actor-addresses/{contract_actor_address_id}")
    public ResponseEntity<SingleResultDto<ContractActorAddressDto>> getContractActorAddress(
            @PathVariable Long contract_actor_id,
            @PathVariable Long contract_actor_address_id) {
        try {
            return contractActorAddressService.getContractActorAddresseById(contract_actor_id, contract_actor_address_id);
        } catch (DataIntegrityViolationException e) {
            throw handleException(e);
        }
    }
}
