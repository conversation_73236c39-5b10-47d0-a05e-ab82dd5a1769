package com.datatricks.contracts.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import java.util.Date;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@MappedSuperclass
@Setter
@Getter
public abstract class BaseEntity {

    @Column(name = "created_at")
    @JsonProperty("created_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonIgnore
    private Date createdAt;

    @Column(name = "modified_at")
    @JsonProperty("modified_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonIgnore
    private Date modifiedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonIgnore
    private Date deletedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = new Date();
    }

    @PreUpdate
    protected void onUpdate() {
        modifiedAt = new Date();
    }

    @PreRemove
    public void onDelete() {
        deletedAt = new Date();
    }
}
